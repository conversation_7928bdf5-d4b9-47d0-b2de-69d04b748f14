* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* Disable text selection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Disable right-click context menu */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Noto Sans Sinhala', sans-serif;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #f4d03f;
  min-height: 100vh;
  overflow-x: hidden;
  /* Additional protection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Disable image dragging and selection */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Disable selection on all text elements */
p, h1, h2, h3, h4, h5, h6, span, div, a {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.App {
  min-height: 100vh;
  position: relative;
}

/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.landing-header {
  text-align: center;
  margin-bottom: 4rem;
  z-index: 2;
  position: relative;
}

.main-title {
  font-size: 3.8rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow:
    0 0 20px rgba(244, 208, 63, 0.6),
    0 0 40px rgba(244, 208, 63, 0.4),
    0 0 60px rgba(244, 208, 63, 0.2);
  margin-bottom: 1.5rem;
  animation: divineGlow 3s ease-in-out infinite alternate;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.8rem;
  color: #e8f4fd;
  font-weight: 400;
  margin-bottom: 2rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1.4;
}

.description {
  font-size: 1.2rem;
  color: #d5dbdb;
  max-width: 700px;
  line-height: 1.8;
  text-align: center;
  margin: 0 auto 2rem auto;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.divine-blessing {
  margin-top: 2rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, rgba(244, 208, 63, 0.1) 0%, rgba(244, 208, 63, 0.05) 100%);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 25px;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.blessing-text {
  color: #f4d03f;
  font-size: 1.1rem;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);
}

/* Premium Zodiac Grid */
.premium-zodiac-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  max-width: 1400px;
  width: 100%;
  z-index: 2;
  padding: 0 1rem;
}

/* Dark Glass Card Design - Consistent with other components */
.dark-glass-card {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: left;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(244, 208, 63, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.dark-glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.1), transparent);
  transition: left 0.5s;
  pointer-events: none;
}

.dark-glass-card:hover::before {
  left: 100%;
}

.dark-glass-card:hover {
  transform: translateY(-10px);
  border-color: rgba(244, 208, 63, 0.5);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 10px 30px rgba(244, 208, 63, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(244, 208, 63, 0.08) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.dark-glass-card:hover .card-glow {
  opacity: 1;
}

.card-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.6), transparent);
  opacity: 0.7;
}

/* Premium Card Content Styles */
.zodiac-header-section {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(244, 208, 63, 0.2);
}

.zodiac-icon-large {
  font-size: 4rem;
  margin-right: 1.5rem;
  color: #f4d03f;
  text-shadow:
    0 0 20px rgba(244, 208, 63, 0.6),
    0 0 40px rgba(244, 208, 63, 0.4);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.dark-glass-card:hover .zodiac-icon-large {
  text-shadow:
    0 0 30px rgba(244, 208, 63, 0.8),
    0 0 60px rgba(244, 208, 63, 0.6);
  transform: scale(1.05);
}

.zodiac-names-section {
  flex: 1;
}

.sinhala-name-large {
  font-size: 2rem;
  font-weight: 700;
  color: #f4d03f;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 10px rgba(244, 208, 63, 0.3);
  line-height: 1.2;
}

.english-name-small {
  font-size: 1.1rem;
  color: #d5dbdb;
  font-weight: 400;
  opacity: 0.9;
}

.zodiac-details {
  margin-bottom: 1.5rem;
  space-y: 0.8rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  padding: 0.6rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.95rem;
  color: #aeb6bf;
  font-weight: 500;
  opacity: 0.9;
}

.detail-value {
  font-size: 0.95rem;
  color: #f4d03f;
  font-weight: 600;
  text-align: right;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.zodiac-description {
  font-size: 1rem;
  color: #d5dbdb;
  font-style: italic;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border-left: 3px solid rgba(244, 208, 63, 0.4);
  line-height: 1.4;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.card-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(244, 208, 63, 0.2);
  margin-top: auto;
  background: rgba(0, 0, 0, 0.05);
  margin: 1rem -2.5rem -2.5rem -2.5rem;
  padding: 1rem 2.5rem;
  backdrop-filter: blur(5px);
}

.action-text {
  font-size: 1rem;
  color: #f4d03f;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.action-arrow {
  font-size: 1.2rem;
  color: #f4d03f;
  transition: transform 0.3s ease;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.dark-glass-card:hover .action-arrow {
  transform: translateX(5px);
}

.dark-glass-card:hover .action-text {
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);
}

/* Zodiac Page Styles */
.zodiac-page {
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.back-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  background: rgba(244, 208, 63, 0.1);
  border: 1px solid #f4d03f;
  color: #f4d03f;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-family: 'Noto Sans Sinhala', sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  z-index: 10;
}

.back-button:hover {
  background: rgba(244, 208, 63, 0.2);
  transform: translateX(-5px);
}

.zodiac-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  padding-top: 4rem;
  z-index: 2;
  position: relative;
}

.zodiac-title {
  font-size: 4rem;
  font-weight: 700;
  color: #f4d03f;
  margin-bottom: 1rem;
  text-shadow: 0 0 30px rgba(244, 208, 63, 0.6);
}

.zodiac-subtitle {
  font-size: 1.5rem;
  color: #d5dbdb;
  margin-bottom: 3rem;
}

.horoscope-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 20px;
  padding: 3rem;
  margin: 2rem 0;
  backdrop-filter: blur(10px);
  max-width: 1200px;
  width: 100%;
}

.horoscope-title {
  font-size: 2rem;
  color: #f4d03f;
  margin-bottom: 1.5rem;
}

.horoscope-content {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #d5dbdb;
  text-align: left;
}

.structured-horoscope-display {
  max-width: 1200px !important;
  width: 100%;
  text-align: left;
}

.horoscope-category-card {
  text-align: left !important;
}

.horoscope-category-card p {
  text-align: left !important;
}

/* Divine Background Image Styles */
.divine-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.god-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.08;
  filter: blur(0.5px) sepia(20%) saturate(150%) hue-rotate(30deg);
  transition: all 0.3s ease;
  object-fit: cover;
  object-position: center;
}

/* Enhance the divine presence on hover */
.zodiac-page:hover .god-image {
  opacity: 0.12;
  filter: blur(0.3px) sepia(25%) saturate(160%) hue-rotate(30deg);
  transform: scale(1.02);
}

/* Additional divine glow effect */
.divine-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(244, 208, 63, 0.03) 0%, transparent 70%);
  z-index: 1;
  animation: divineGlow 4s ease-in-out infinite alternate;
}

@keyframes divineGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.loading {
  font-size: 1.1rem;
  color: #aeb6bf;
  font-style: italic;
}

.error {
  color: #e74c3c;
  font-size: 1.1rem;
}

/* Animations */
@keyframes divineGlow {
  0% {
    text-shadow:
      0 0 20px rgba(244, 208, 63, 0.6),
      0 0 40px rgba(244, 208, 63, 0.4),
      0 0 60px rgba(244, 208, 63, 0.2);
  }
  50% {
    text-shadow:
      0 0 30px rgba(244, 208, 63, 0.8),
      0 0 60px rgba(244, 208, 63, 0.6),
      0 0 90px rgba(244, 208, 63, 0.4);
  }
  100% {
    text-shadow:
      0 0 20px rgba(244, 208, 63, 0.6),
      0 0 40px rgba(244, 208, 63, 0.4),
      0 0 60px rgba(244, 208, 63, 0.2);
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px rgba(244, 208, 63, 0.5);
  }
  to {
    text-shadow: 0 0 30px rgba(244, 208, 63, 0.8), 0 0 40px rgba(244, 208, 63, 0.6);
  }
}

@keyframes premiumFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-5px) rotate(0.5deg);
  }
  50% {
    transform: translateY(-10px) rotate(0deg);
  }
  75% {
    transform: translateY(-5px) rotate(-0.5deg);
  }
}

.premium-zodiac-card {
  animation: premiumFloat 6s ease-in-out infinite;
}

.premium-zodiac-card:nth-child(even) {
  animation-delay: -3s;
}

.premium-zodiac-card:nth-child(3n) {
  animation-delay: -1.5s;
}

.premium-zodiac-card:nth-child(4n) {
  animation-delay: -4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Particles Background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* Smoke Animation */
.smoke-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.smoke {
  position: absolute;
  bottom: -50px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(244, 208, 63, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: smokeRise 8s linear infinite;
}

@keyframes smokeRise {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100vh) scale(2);
    opacity: 0;
  }
}

/* Enhanced Mobile Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .landing-page {
    padding: 1.5rem;
  }
  
  .zodiac-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
  }
  
  .main-title {
    font-size: 3rem;
  }
  
  .zodiac-title {
    font-size: 3.5rem;
  }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  /* Adjust divine background for tablets */
  .god-image {
    opacity: 0.15;
  }
  
  .main-title {
    font-size: 2.8rem;
    margin-bottom: 1rem;
    letter-spacing: 1px;
  }

  .subtitle {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
  }

  .description {
    font-size: 1rem;
    padding: 0 1.5rem;
    max-width: 90%;
  }

  .divine-blessing {
    margin: 1.5rem 1rem 0 1rem;
    padding: 0.8rem 1.5rem;
  }

  .blessing-text {
    font-size: 1rem;
  }

  .premium-zodiac-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  .dark-glass-card {
    padding: 2rem 1.5rem;
  }

  .card-action {
    margin: 1rem -1.5rem -2rem -1.5rem;
    padding: 1rem 1.5rem;
  }

  .zodiac-header-section {
    flex-direction: column;
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .zodiac-icon-large {
    font-size: 3.5rem;
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .sinhala-name-large {
    font-size: 1.8rem;
  }

  .english-name-small {
    font-size: 1rem;
  }

  .detail-row {
    flex-direction: column;
    text-align: center;
    gap: 0.3rem;
  }

  .detail-value {
    text-align: center;
  }

  .zodiac-description {
    font-size: 0.95rem;
    text-align: center;
  }
  
  .zodiac-title {
    font-size: 2.5rem;
    margin-bottom: 0.8rem;
  }
  
  .zodiac-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
  }
  
  .horoscope-section {
    padding: 2rem 1.5rem;
    margin: 1.5rem 0;
  }
  
  .horoscope-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }
  
  .horoscope-content {
    font-size: 1.1rem;
    line-height: 1.7;
  }
  
  .back-button {
    top: 1rem;
    left: 1rem;
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
  
  .zodiac-content {
    padding-top: 3.5rem;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  .landing-page {
    padding: 1rem 0.5rem;
  }
  
  .zodiac-page {
    padding: 1rem 0.5rem;
  }
  
  /* Adjust divine background for mobile */
  .god-image {
    opacity: 0.12;
  }
  
  .main-title {
    font-size: 2.2rem;
    line-height: 1.2;
  }

  .subtitle {
    font-size: 1.2rem;
    padding: 0 0.5rem;
  }

  .description {
    font-size: 0.95rem;
    padding: 0 0.5rem;
  }

  .divine-blessing {
    margin: 1rem 0.5rem 0 0.5rem;
    padding: 0.6rem 1rem;
  }

  .blessing-text {
    font-size: 0.9rem;
  }

  .premium-zodiac-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .dark-glass-card {
    padding: 1.5rem 1rem;
  }

  .card-action {
    margin: 1rem -1rem -1.5rem -1rem;
    padding: 1rem;
  }

  .zodiac-icon-large {
    font-size: 3rem;
    margin-bottom: 0.8rem;
  }

  .sinhala-name-large {
    font-size: 1.5rem;
  }

  .english-name-small {
    font-size: 0.9rem;
  }

  .zodiac-details {
    margin-bottom: 1rem;
  }

  .detail-row {
    margin-bottom: 0.6rem;
    padding: 0.3rem 0;
  }

  .detail-label, .detail-value {
    font-size: 0.85rem;
  }

  .zodiac-description {
    font-size: 0.9rem;
    padding: 0.8rem;
    margin-bottom: 1rem;
  }

  .card-action {
    padding-top: 0.8rem;
  }

  .action-text {
    font-size: 0.9rem;
  }
  
  .zodiac-title {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .zodiac-subtitle {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }
  
  .horoscope-section {
    padding: 1.5rem 1rem;
    margin: 1rem 0;
  }
  
  .horoscope-title {
    font-size: 1.5rem;
  }
  
  .horoscope-content {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .back-button {
    top: 0.8rem;
    left: 0.8rem;
    padding: 0.5rem 0.8rem;
    font-size: 0.85rem;
  }
  
  .zodiac-content {
    padding-top: 3rem;
  }
  
  .controls {
    margin-top: 1.5rem !important;
  }
  
  .sound-toggle {
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem !important;
  }
  
  .spiritual-message {
    margin-top: 2rem !important;
    padding: 1.5rem !important;
  }
  
  .spiritual-message p {
    font-size: 1rem !important;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .main-title {
    font-size: 1.8rem;
  }
  
  .zodiac-grid {
    gap: 0.6rem;
  }
  
  .zodiac-card {
    padding: 1rem 0.6rem;
  }
  
  .zodiac-icon {
    font-size: 2rem;
  }
  
  .zodiac-name {
    font-size: 0.9rem;
  }
  
  .zodiac-english {
    font-size: 0.8rem;
  }
  
  .zodiac-title {
    font-size: 1.8rem;
  }
  
  .horoscope-section {
    padding: 1.2rem 0.8rem;
  }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
  .landing-page {
    padding: 1rem;
  }
  
  .main-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  .description {
    font-size: 0.9rem;
  }
  
  .zodiac-grid {
    gap: 0.8rem;
  }
  
  .zodiac-card {
    padding: 1rem;
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .zodiac-card {
    transition: transform 0.2s ease;
  }
  
  .zodiac-card:active {
    transform: scale(0.98);
  }
  
  .back-button:active {
    transform: scale(0.95);
  }
  
  .sound-toggle:active {
    transform: scale(0.95);
  }
}

/* Kubera Guide Page Styles */
.kubera-guide-page {
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.zodiac-navigation {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  z-index: 2;
  position: relative;
}

.zodiac-nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 15px;
  color: #f4d03f;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.zodiac-nav-btn:hover {
  transform: translateY(-3px);
  border-color: rgba(244, 208, 63, 0.5);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.zodiac-nav-btn .nav-icon {
  font-size: 1.3rem;
}

.zodiac-nav-btn .nav-arrow {
  transition: transform 0.3s ease;
}

.zodiac-nav-btn:hover .nav-arrow {
  transform: translateX(5px);
}

.back-to-guide-btn {
  padding: 0.8rem 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 10px;
  color: #f4d03f;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.back-to-guide-btn:hover {
  transform: translateY(-2px);
  border-color: rgba(244, 208, 63, 0.5);
}

.kubera-content-section {
  margin-bottom: 3rem;
  display: flex;
  justify-content: center;
}

.kubera-content-card {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  animation: fadeInUp 0.8s ease-out;
}

.content-header {
  margin-bottom: 2rem;
  text-align: center;
}

.content-title {
  font-size: 2rem;
  color: #f4d03f;
  font-weight: 600;
  text-shadow: 0 0 20px rgba(244, 208, 63, 0.4);
  margin-bottom: 1rem;
}

.content-body {
  line-height: 1.8;
  font-size: 1.1rem;
  color: #e8f4fd;
}

.content-body p {
  margin-bottom: 1.5rem;
  text-align: justify;
}

/* Mantra Card Specific Styles */
.mantra-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(244, 208, 63, 0.02));
}

.mantra-section {
  text-align: center;
}

.mantra-subtitle {
  font-size: 1.3rem;
  color: #f4d03f;
  margin: 2rem 0 1rem 0;
  font-weight: 500;
}

.sanskrit-mantra {
  font-size: 1.4rem;
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  margin: 1rem 0;
  border: 1px solid rgba(255, 215, 0, 0.2);
  font-family: 'Noto Sans Devanagari', serif;
  line-height: 1.6;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.sinhala-pronunciation {
  font-size: 1.3rem;
  color: #e8f4fd;
  background: rgba(232, 244, 253, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  margin: 1rem 0;
  border: 1px solid rgba(232, 244, 253, 0.2);
  line-height: 1.6;
}

.mantra-meaning {
  font-size: 1.1rem;
  color: #d5dbdb;
  background: rgba(213, 219, 219, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  margin: 1rem 0;
  border: 1px solid rgba(213, 219, 219, 0.2);
  text-align: justify;
  line-height: 1.7;
}

/* Usage Guidelines Styles */
.usage-guidelines {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.guideline-item {
  background: rgba(255, 255, 255, 0.02);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #f4d03f;
}

.guideline-title {
  font-size: 1.2rem;
  color: #f4d03f;
  margin-bottom: 0.8rem;
  font-weight: 600;
}

.guideline-item p {
  margin-bottom: 0;
  color: #e8f4fd;
}

/* Benefits Card Styles */
.benefits-card {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.05), rgba(255, 255, 255, 0.02));
}

.benefits-list {
  display: grid;
  gap: 1.5rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.02);
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid rgba(46, 204, 113, 0.2);
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-2px);
  border-color: rgba(46, 204, 113, 0.4);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.benefit-icon {
  font-size: 2rem;
  min-width: 3rem;
  text-align: center;
}

.benefit-content h4 {
  font-size: 1.2rem;
  color: #2ecc71;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.benefit-content p {
  margin-bottom: 0;
  color: #e8f4fd;
  line-height: 1.6;
}

/* Important Notes Card Styles */
.important-notes-card {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.05), rgba(255, 255, 255, 0.02));
  border-color: rgba(231, 76, 60, 0.3);
}

.important-note {
  background: rgba(231, 76, 60, 0.1);
  padding: 2rem;
  border-radius: 10px;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.important-note p {
  color: #ffeaa7;
  font-weight: 500;
  text-align: justify;
  margin-bottom: 1.5rem;
}

.important-note p:last-child {
  margin-bottom: 0;
}

/* Footer Styles */
.kubera-footer {
  margin-top: 4rem;
  text-align: center;
  padding: 2rem 0;
}

.kubera-footer .divine-blessing {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem 2rem;
  border-radius: 15px;
  border: 1px solid rgba(244, 208, 63, 0.3);
  display: inline-block;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive Design for Kubera Guide */
@media (max-width: 768px) {
  .kubera-guide-page {
    padding: 1rem 0.5rem;
  }

  .content-title {
    font-size: 1.6rem;
  }

  .content-body {
    font-size: 1rem;
  }

  .sanskrit-mantra {
    font-size: 1.2rem;
    padding: 1rem;
  }

  .sinhala-pronunciation {
    font-size: 1.1rem;
    padding: 1rem;
  }

  .mantra-meaning {
    font-size: 1rem;
    padding: 1rem;
  }

  .benefit-item {
    flex-direction: column;
    text-align: center;
  }

  .benefit-icon {
    min-width: auto;
  }

  .zodiac-nav-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.4rem;
  }

  .content-title {
    font-size: 1.4rem;
  }

  .kubera-content-card {
    padding: 1.5rem;
  }

  .guideline-item {
    padding: 1rem;
  }

  .benefit-item {
    padding: 1rem;
  }

  .important-note {
    padding: 1.5rem;
  }
}